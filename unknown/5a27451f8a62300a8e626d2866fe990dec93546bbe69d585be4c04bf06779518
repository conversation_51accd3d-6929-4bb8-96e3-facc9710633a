'use client'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { useCallback, useEffect, useRef, useState } from 'react'

import { useTranslations } from 'next-intl'
import { useSidebar } from '@/contexts/Sidebar/SidebarContext'
import { ChevronDownIcon, GridIcon, UsersIcon } from 'lucide-react'
import menuIcon from '@/assets/icons/menu-white.svg'
import { Button } from '../ui/Button/Button'

// Image
import homeIcon from '@/assets/icons/home-white.svg'
import dictionaryIcon from '@/assets/icons/book-white.svg'
import bookHandIcon from '@/assets/icons/insurance-hands-white.svg'
import medicineIcon from '@/assets/icons/drugs-pill-white.svg'
import newsIcon from '@/assets/icons/document-white.svg'
import examinationIcon from '@/assets/icons/medical-examination-icon-white.svg'

type NavItem = {
  name: string
  urlIcon?: string
  path?: string
  subItems?: { name: string; path: string; pro?: boolean; new?: boolean }[]
}

const AppSidebar: React.FC = () => {
  const { isExpanded, isMobileOpen, isHovered, setIsHovered } = useSidebar()
  const pathname = usePathname()

  const t = useTranslations()

  const navItems: NavItem[] = [
    {
      name: t('MES-37'),
      path: '/',
      urlIcon: homeIcon,
      // permission: Permission.VIEW_PROJECTS,
    },
    {
      name: t('MES-577'),
      path: '/',
      urlIcon: dictionaryIcon,
      // permission: Permission.VIEW_PROMPTS,
    },
    {
      name: t('MES-33'),
      path: '/',
      urlIcon: bookHandIcon,
      // permission: Permission.VIEW_PROMPTS,
    },
    {
      name: t('MES-561'),
      urlIcon: medicineIcon,
      // permission: Permission.VIEW_PROMPTS,
      subItems: [
        {
          name: t('MES-721'),
          path: '',
        },
        {
          name: t('MES-471'),
          path: '',
        },
        {
          name: t('MES-722'),
          path: '',
        },
      ],
    },
    {
      name: t('MES-19'),
      path: '/',
      urlIcon: newsIcon,
      // permission: Permission.VIEW_PROMPTS,
      subItems: [
        {
          name: t('MES-20'),
          path: '',
        },
        {
          name: t('MES-723'),
          path: '',
        },
        {
          name: t('MES-724'),
          path: '',
        },
        {
          name: t('MES-725'),
          path: '',
        },
        {
          name: t('MES-726'),
          path: '',
        },
      ],
    },
    {
      name: t('MES-720'),
      path: '/',
      urlIcon: examinationIcon,
      // permission: Permission.VIEW_PROMPTS,
    },

    // {
    //   icon: <RocketLaunchIcon />,
    //   name: 'Workflow Prompts',
    //   path: '/dashboard/steps',
    //   permission: Permission.VIEW_STEPS,
    // },
    // {
    //   name: t('teamMembers'),
    //   path: '/dashboard/team-members',
    //   // permission: Permission.VIEW_USERS,
    // },
    // {
    //   name: t('framework'),
    //   path: '/dashboard/frameworks-templates',
    //   // permission: Permission.VIEW_FRAMEWORKS_TEMPLATES,
    // },
  ]

  const [openSubmenu, setOpenSubmenu] = useState<{
    type: 'main' | 'others'
    index: number
  } | null>(null)
  const [subMenuHeight, setSubMenuHeight] = useState<Record<string, number>>({})
  const subMenuRefs = useRef<Record<string, HTMLDivElement | null>>({})

  // const isActive = (path: string) => path === pathname;
  const isActive = useCallback((path: string) => pathname.includes(path), [pathname])

  useEffect(() => {
    // Check if the current path matches any submenu item
    let submenuMatched = false
    ;['main', 'others'].forEach((menuType) => {
      const items = menuType === 'main' ? navItems : []
      items.forEach((nav, index) => {
        if (nav.subItems) {
          nav.subItems.forEach((subItem) => {
            if (isActive(subItem.path)) {
              setOpenSubmenu({
                type: menuType as 'main' | 'others',
                index,
              })
              submenuMatched = true
            }
          })
        }
      })
    })

    // If no submenu item matches, close the open submenu
    if (!submenuMatched) {
      setOpenSubmenu(null)
    }
  }, [pathname, isActive])

  useEffect(() => {
    // Set the height of the submenu items when the submenu is opened
    if (openSubmenu !== null) {
      const key = `${openSubmenu.type}-${openSubmenu.index}`
      if (subMenuRefs.current[key]) {
        setSubMenuHeight((prevHeights) => ({
          ...prevHeights,
          [key]: subMenuRefs.current[key]?.scrollHeight || 0,
        }))
      }
    }
  }, [openSubmenu])

  const handleSubmenuToggle = (index: number, menuType: 'main' | 'others') => {
    setOpenSubmenu((prevOpenSubmenu) => {
      if (prevOpenSubmenu && prevOpenSubmenu.type === menuType && prevOpenSubmenu.index === index) {
        return null
      }
      return { type: menuType, index }
    })
  }

  const renderMenuItems = (navItems: NavItem[], menuType: 'main' | 'others') => (
    <ul className="flex flex-col gap-4 text-white">
      {navItems.map((nav, index) => (
        // <PermissionGuard permission={nav.permission} key={nav.name}>
        <>
          <li>
            {nav.subItems ? (
              <button
                type="button"
                onClick={() => handleSubmenuToggle(index, menuType)}
                className={`menu-item group flex items-center gap-2 ${
                  openSubmenu?.type === menuType && openSubmenu?.index === index
                    ? 'menu-item-active'
                    : 'menu-item-inactive'
                } cursor-pointer ${
                  !isExpanded && !isHovered ? 'lg:justify-center' : 'lg:justify-start'
                }`}
              >
                <span
                  className={`size-5 ${
                    openSubmenu?.type === menuType && openSubmenu?.index === index
                      ? 'menu-item-icon-active'
                      : 'menu-item-icon-inactive'
                  }`}
                >
                  {nav.urlIcon && (
                    <Image
                      className="inline-block"
                      src={nav.urlIcon}
                      alt="Logo"
                      width={24}
                      height={24}
                    />
                  )}
                </span>
                {(isExpanded || isHovered || isMobileOpen) && (
                  <span className="menu-item-text">{nav.name}</span>
                )}
                {(isExpanded || isHovered || isMobileOpen) && (
                  <ChevronDownIcon
                    className={`ml-auto size-5 transition-transform duration-200 ${
                      openSubmenu?.type === menuType && openSubmenu?.index === index
                        ? 'text-brand-500 rotate-180'
                        : ''
                    }`}
                  />
                )}
              </button>
            ) : (
              nav.path && (
                <Link
                  href={nav.path}
                  className={`menu-item group flex items-center gap-2 ${
                    isActive(nav.path) ? 'menu-item-active' : 'menu-item-inactive'
                  }`}
                >
                  <span
                    className={`size-6 ${
                      isActive(nav.path) ? 'menu-item-icon-active' : 'menu-item-icon-inactive'
                    }`}
                  >
                    {nav.urlIcon && (
                      <Image
                        className="inline-block"
                        src={nav.urlIcon}
                        alt="Logo"
                        height={24}
                        width={24}
                      />
                    )}
                  </span>
                  {(isExpanded || isHovered || isMobileOpen) && (
                    <span className="menu-item-text typo-body-6">{nav.name}</span>
                  )}
                </Link>
              )
            )}
            {nav.subItems && (isExpanded || isHovered || isMobileOpen) && (
              <div
                ref={(el) => {
                  subMenuRefs.current[`${menuType}-${index}`] = el
                }}
                className="overflow-hidden transition-all duration-300"
                style={{
                  height:
                    openSubmenu?.type === menuType && openSubmenu?.index === index
                      ? `${subMenuHeight[`${menuType}-${index}`]}px`
                      : '0px',
                }}
              >
                <ul className="ml-9 mt-2 flex flex-col gap-3 space-y-1">
                  {nav.subItems.map((subItem) => (
                    <li key={subItem.name}>
                      <Link
                        href={subItem.path}
                        className={`menu-dropdown-item ${
                          isActive(subItem.path)
                            ? 'menu-dropdown-item-active'
                            : 'menu-dropdown-item-inactive'
                        }`}
                      >
                        {subItem.name}
                        {/* <span className="ml-auto flex items-center gap-1">
                          {subItem.new && (
                            <span
                              className={`ml-auto ${
                                isActive(subItem.path)
                                  ? 'menu-dropdown-badge-active'
                                  : 'menu-dropdown-badge-inactive'
                              } menu-dropdown-badge`}
                            >
                              new
                            </span>
                          )}
                          {subItem.pro && (
                            <span
                              className={`ml-auto ${
                                isActive(subItem.path)
                                  ? 'menu-dropdown-badge-active'
                                  : 'menu-dropdown-badge-inactive'
                              } menu-dropdown-badge`}
                            >
                              pro
                            </span>
                          )}
                        </span> */}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </li>
        </>
      ))}
    </ul>
  )

  return (
    <aside
      className={`fixed left-0 top-0 z-50 mt-16 flex h-screen flex-col border-r border-border bg-[linear-gradient(to_bottom,#1157C8,#3D8DEF,#639BF4)] px-5 text-foreground transition-all duration-300 ease-in-out lg:mt-0 ${
        isExpanded || isMobileOpen ? 'w-[300px]' : isHovered ? 'w-[300px]' : 'w-[90px]'
      } ${isMobileOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0`}
      onMouseEnter={() => !isExpanded && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={`flex py-8 ${!isExpanded && !isHovered ? 'lg:justify-center' : 'justify-start'}`}
      >
        <Button className="p-0">
          {isExpanded || isHovered || isMobileOpen ? (
            <div className="flex items-center rounded-lg bg-[rgba(191,237,254,0.15)] p-2">
              <Image className="inline-block" src={menuIcon} alt="Logo" width={24} height={24} />
            </div>
          ) : (
            <div className="flex items-center rounded-lg bg-[rgba(191,237,254,0.15)] p-2">
              <Image className="inline-block" src={menuIcon} alt="Logo" width={24} height={24} />
            </div>
          )}
        </Button>
      </div>
      <div className="no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear">
        <nav className="mb-6">
          <div className="flex flex-col gap-4">
            <div>
              {/* <h2
                className={`mb-4 text-xs uppercase flex leading-[20px] text-gray-400 ${!isExpanded && !isHovered
                  ? 'lg:justify-center'
                  : 'justify-start'
                }`}
              >
                {isExpanded || isHovered || isMobileOpen
                  ? (
                      'Menu'
                    )
                  : (
                      <HorizontaLDots />
                    )}
              </h2> */}
              {renderMenuItems(navItems, 'main')}
            </div>

            {/* <div className="">
              <h2
                className={`mb-4 text-xs uppercase flex leading-[20px] text-gray-400 ${!isExpanded && !isHovered
                  ? 'lg:justify-center'
                  : 'justify-start'
                }`}
              >
                {isExpanded || isHovered || isMobileOpen
                  ? (
                      'Others'
                    )
                  : (
                      <HorizontaLDots />
                    )}
              </h2>
              {renderMenuItems(othersItems, 'others')}
            </div> */}
          </div>
        </nav>
      </div>
    </aside>
  )
}

export default AppSidebar
